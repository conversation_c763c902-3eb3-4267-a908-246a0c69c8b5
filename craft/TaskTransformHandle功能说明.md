# TaskTransformHandle 功能说明

## 概述

`TaskTransformHandle` 类新增了为 Telegram payload 添加 taskCode 字段的功能。该功能在处理历史遗留数据时，会根据 Telegram 的 serviceId 判断是否需要为其 payload 添加 taskCode 字段。

## 功能特性

### 1. 常量集合定义

定义了一个常量集合 `TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE`，用于存储需要特殊处理的 Telegram serviceId：

```java
private static final Set<String> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE = Set.of(
    "service_id_1",
    "service_id_2", 
    "service_id_3"
    // 可以根据实际需求添加更多 serviceId
);
```

### 2. 智能 payload 处理

在 `dealLegacyData` 方法中，会：

1. **检查 serviceId**：判断 Telegram 的 serviceId 是否在预定义的集合中
2. **获取 taskCode 集合**：从 handleContext 中获取 taskCode 集合
3. **修改 payload**：为符合条件的 Telegram payload 添加 taskCode 字段

### 3. 支持多种 payload 格式

#### 单个对象格式
```json
{
  "name": "test",
  "value": 123
}
```

处理后：
```json
{
  "name": "test", 
  "value": 123,
  "taskCode": "TASK001"
}
```

#### 数组格式
```json
[
  {"name": "test1", "value": 111},
  {"name": "test2", "value": 222}
]
```

处理后：
```json
[
  {"name": "test1", "value": 111, "taskCode": "TASK001"},
  {"name": "test2", "value": 222, "taskCode": "TASK002"}
]
```

## 使用方法

### 1. 配置 serviceId

在 `TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE` 常量中添加需要特殊处理的 serviceId：

```java
private static final Set<String> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE = Set.of(
    "your_service_id_1",
    "your_service_id_2",
    "your_service_id_3"
);
```

### 2. 确保 handleContext 包含 taskCode

在调用 `dealLegacyData` 方法之前，确保 handleContext 中包含 taskCode 集合：

```java
Map<String, Object> handleContext = new HashMap<>();
List<String> taskCodes = Arrays.asList("TASK001", "TASK002", "TASK003");
handleContext.put("taskCode", taskCodes);
```

### 3. 自动处理

当满足以下条件时，系统会自动为 payload 添加 taskCode 字段：

- Telegram 的 serviceId 在预定义集合中
- handleContext 中包含非空的 taskCode 集合
- payload 是有效的 JSON 格式

## 错误处理

### 1. 异常安全

如果在处理 payload 时发生异常，系统会：
- 记录错误日志
- 返回原始 payload，确保消息能正常推送

### 2. 数据验证

- 自动验证 JSON 格式
- 检查 taskCode 集合是否为空
- 确保 payload 类型正确（ObjectNode 或 ArrayNode）

## 性能考虑

### 1. 条件判断优化

只有当 serviceId 匹配且 taskCode 集合非空时，才会进行 JSON 解析和修改操作。

### 2. 内存使用

使用 Jackson 的 JsonNode 进行内存中的 JSON 操作，避免字符串拼接。

## 测试

提供了完整的单元测试，覆盖以下场景：

1. 单个对象 payload 处理
2. 数组 payload 处理  
3. 空 taskCode 集合处理
4. 无效 JSON 处理

运行测试：
```bash
mvn test -Dtest=TaskTransformHandleTest
```

## 注意事项

1. **serviceId 配置**：需要根据实际业务需求配置正确的 serviceId
2. **taskCode 顺序**：数组 payload 中的 taskCode 按索引顺序分配
3. **JSON 格式**：确保 payload 是有效的 JSON 格式
4. **向后兼容**：不影响现有功能，只在特定条件下生效
