package com.bzlj.craft.transform.handle;


import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.entity.Telegram;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.DateTimeConverter;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 任务分发转换处理器
 * <p>
 * 负责处理任务分发消息的转换和处理，主要功能包括：
 * 1. 解析任务分发JSON消息
 * 2. 创建生产任务及相关实体
 * 3. 建立任务与设备的关联关系
 * 4. 生成执行工步
 * 5. 处理任务扩展属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 10:42
 */
@Slf4j
@MessageHandler(messageType = "task_Distribute", desc = "任务转化")
public class TaskTransformHandle extends CommonHandler<String> {

    /**
     * 数据准备服务，用于获取基础数据
     */
    @Autowired
    private DataPrepareService dataPrepareService;

    /**
     * 工艺流程仓储
     */
    @Autowired
    private CraftProcessRepository craftProcessRepository;

    /**
     * 工艺步骤仓储
     */
    @Autowired
    private ProcessStepRepository processStepRepository;

    /**
     * 工步参数仓储
     */
    @Autowired
    private StepParameterRepository stepParameterRepository;

    /**
     * 参数定义仓储
     */
    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    /**
     * 设备仓储
     */
    @Autowired
    private EquipmentRepository equipmentRepository;

    /**
     * 生产任务仓储
     */
    @Autowired
    private ProductionTaskRepository productionTaskRepository;

    /**
     * 生产任务扩展仓储
     */
    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    /**
     * 任务设备关联仓储
     */
    @Autowired
    private TaskEquipmentRepository taskEquipmentRepository;

    /**
     * 工作步骤仓储
     */
    @Autowired
    private WorkStepRepository workStepRepository;

    /**
     * 电报仓储
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 消息事件生产者
     */
    @Autowired
    private MessageEventProducer messageEventProducer;

    @Autowired
    private ISurveillanceService surveillanceService;


    /**
     * 任务转换处理方法
     * <p>
     * 解析任务分发JSON消息，创建生产任务及相关实体。处理流程：
     * 1. 验证分厂、工序等基础数据
     * 2. 检查任务是否已存在
     * 3. 创建生产任务实体
     * 4. 处理任务扩展属性
     * 5. 建立任务与设备的关联
     * 6. 生成执行工步
     * </p>
     *
     * @param json 任务分发JSON字符串，支持单个任务或任务数组
     * @throws RuntimeException 当必要字段缺失或基础数据不存在时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext){

        // 获取任务初始状态（未开始）
        SysDictItem dictItem = dataPrepareService.getStatusDictItem(TaskStatus.not_start.getCode());

        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        // 如果是数组，递归处理每个元素
        if(jsonNode instanceof ArrayNode){
            jsonNode.forEach(node -> {
                transform(node.toString(), handleContext);
            });
            return;
        }

        // 验证分厂信息
        if(Objects.isNull(jsonNode.get("plantCode"))){
            throw new RuntimeException("分厂为空");
        }
        String plantCode = jsonNode.get("plantCode").asText();
        Plant plant = dataPrepareService.getPlant(plantCode);
        if(Objects.isNull(plant)){
            throw new RuntimeException(String.format("%s:该分厂不存在,请先导入分厂",  jsonNode.get("plantCode").asText()));
        }

        // 检查任务是否已存在
        String taskCode = jsonNode.get("taskCode").asText();
        ProductionTask task = productionTaskRepository.findFirstByTaskCodeAndDeleted(taskCode, false);
        if(Objects.nonNull(task)) {
            log.warn("{}:任务已存在，删除原有任务重新生成", taskCode);
            surveillanceService.deleteTask(task.getTaskId());
            log.warn("{}:原任务及关联数据已删除", taskCode);
        }

        // 验证工序信息
        if(Objects.isNull(jsonNode.get("processCode"))){
            throw new RuntimeException("工序为空");
        }
        CraftProcess process = craftProcessRepository.findFirstByProcessCode(jsonNode.get("processCode").asText());
        if(Objects.isNull(process)){
            throw new RuntimeException(String.format("%s:该工序不存在,请先导入工序",  jsonNode.get("processCode").asText()));
        }

        // 获取工序步骤
        List<ProcessStep> steps = processStepRepository.findByProcessIdOrderByStepOrderAsc(process.getId());
        if(CollectionUtils.isEmpty(steps)){
            throw new RuntimeException(String.format("%s:缺少工步信息",  jsonNode.get("processCode").asText()));
        }

        // 创建生产任务实体
        ProductionTask productionTask = new ProductionTask();
        productionTask.setPlant(plant);
        productionTask.setTaskCode(jsonNode.get("taskCode").asText());
        productionTask.setProcess(process);
        productionTask.setTaskName(jsonNode.get("taskName").asText());
        productionTask.setStep(steps.get(0)); // 设置为第一个工步


        if(Objects.nonNull(jsonNode.get("craftCode")) && StringUtils.isNotBlank(jsonNode.get("craftCode").asText())){
            productionTask.setCraftCode(jsonNode.get("craftCode").asText());
        }

        // 设置可选字段
        if(Objects.nonNull(jsonNode.get("heatNumber")) && StringUtils.isNotBlank(jsonNode.get("heatNumber").asText())){
            productionTask.setHeatNo(jsonNode.get("heatNumber").asText());
        }
        if(Objects.nonNull(jsonNode.get("planStartTime")) && StringUtils.isNotBlank(jsonNode.get("planStartTime").asText())){
            productionTask.setPlanStartTime(DateTimeConverter.convertToLocalDateTime(jsonNode.get("planStartTime").asText()));
        }
        if(Objects.nonNull(jsonNode.get("planEndTime")) && StringUtils.isNotBlank(jsonNode.get("planEndTime").asText())){
            productionTask.setPlanEndTime(DateTimeConverter.convertToLocalDateTime(jsonNode.get("planEndTime").asText()));
        }
        if (Objects.nonNull(jsonNode.get("planWeight")) && StringUtils.isNotBlank(jsonNode.get("planWeight").asText())) {
            productionTask.setPlanWeight(new BigDecimal(jsonNode.get("planWeight").asText()));
        }
        productionTask.setStatusCode(dictItem);
        if(!Objects.isNull(jsonNode.get("planQuantity"))) {
            productionTask.setPlanQuantity(jsonNode.get("planQuantity").asInt());
        }

        // 保存生产任务
        productionTask = productionTaskRepository.save(productionTask);

        // 处理扩展属性
        JsonNode extendAttr = jsonNode.get("extendAttr");
        if(Objects.nonNull(extendAttr)){
            ProductionTaskExtend productionTaskExtend = new ProductionTaskExtend();
            productionTaskExtend.setTask(productionTask);
            productionTaskExtend.setExtendAttr(JsonUtils.toMap(extendAttr));
            productionTaskExtendRepository.save(productionTaskExtend);
        }

        // 验证并关联设备
        if(Objects.isNull(jsonNode.get("equipmentCode"))){
            throw new RuntimeException("设备编号为空");
        }
        Equipment equipment = transformEquipment(jsonNode.get("equipmentCode").asText());
        TaskEquipment taskEquipment = new TaskEquipment();
        taskEquipment.setId(new TaskEquipmentId(productionTask.getTaskId(), equipment.getId()));
        taskEquipment.setTask(productionTask);
        taskEquipment.setEquip(equipment);
        taskEquipmentRepository.save(taskEquipment);

        // 创建执行工步及执行工艺参数
        saveWorkSteps(steps, productionTask);

        List<String> taskCodes = (List<String>) handleContext.getOrDefault("taskCode", new ArrayList<>());
        taskCodes.add(productionTask.getTaskCode());
    }

    /**
     * 获取关联ID列表
     * <p>
     * 从JSON消息中提取任务编号和工艺编号作为关联ID，
     * 用于后续的历史数据处理
     * </p>
     *
     * @param json JSON消息字符串
     * @return 关联ID列表，包含任务编号和工艺编号
     */
    @Override
    public List<String> getRelationIds(String json) {
        List<String> relationIds = new ArrayList<>();
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        if(jsonNode instanceof ArrayNode){
            jsonNode.forEach(node -> {
                if(Objects.nonNull(node.get("taskCode"))){
                    relationIds.add(node.get("taskCode").asText());
                }
                if(Objects.nonNull(node.get("craftCode"))){
                    relationIds.add(node.get("craftCode").asText());
                }
            });
        }
        return relationIds;
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除对应的电报记录
     * </p>
     *
     * @param telegramId 电报ID
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 根据关联ID列表查找相关的历史电报，
     * 并重新推送这些电报消息进行处理
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        if(CollectionUtils.isEmpty(relationIds)) return;
        List<Telegram> telegrams = telegramRepository.findByRelationIdIn(relationIds);
        if(CollectionUtils.isEmpty(telegrams)) return;
        telegrams.stream().forEach(telegram -> {
            MessageEvent event = new MessageEvent();
            event.setMessageId(telegram.getId());
            event.setMessageType(telegram.getType());
            event.setPayload(telegram.getPayload());
            messageEventProducer.pushMessage(event);
        });
    }

    /**
     * 转换设备信息
     * <p>
     * 根据设备编号查找设备实体，如果设备不存在则抛出异常
     * </p>
     *
     * @param equipmentCode 设备编号
     * @return 设备实体
     * @throws RuntimeException 当设备编号为空或设备不存在时抛出异常
     */
    /**
     * 转换设备信息
     * <p>
     * 根据设备编码查询设备实体，用于建立任务与设备的关联关系
     * </p>
     *
     * @param equipmentCode 设备编码
     * @return 设备实体
     * @throws RuntimeException 当设备编码为空或设备不存在时抛出异常
     */
    private Equipment transformEquipment(String equipmentCode){
        if(StringUtils.isEmpty(equipmentCode)){
            throw new RuntimeException("设备编号为空");
        }
        Equipment equipment = equipmentRepository.findFirstByCode(equipmentCode);
        if(Objects.isNull(equipment)){
            throw new RuntimeException(String.format("%s:该设备不存在,请先导入设备", equipmentCode));
        }
        return equipment;
    }

    /**
     * 保存执行工步
     * <p>
     * 根据工艺步骤列表为指定任务创建对应的工作步骤，
     * 工作步骤继承工艺步骤的名称和顺序。
     * 创建工作步骤后，会查询ProcessStep下的stepParameter对象，
     * 根据workStep及stepParameter创建ParameterDefinition对象。
     * </p>
     *
     * @param steps 工艺步骤列表
     * @param task 生产任务
     * @return 创建的工作步骤列表
     */
    private List<WorkStep> saveWorkSteps(List<ProcessStep> steps, ProductionTask task){
        // 创建工作步骤
        List<WorkStep> workSteps = steps.stream().map(step -> {
            WorkStep workStep = new WorkStep();
            workStep.setStep(step);
            workStep.setTask(task);
            workStep.setWorkStepName(step.getStepName());
            workStep.setWorkStepOrder(step.getStepOrder());
            return workStep;
        }).toList();

        // 保存工作步骤
        List<WorkStep> savedWorkSteps = workStepRepository.saveAll(workSteps);

        // 为每个工作步骤创建参数定义
        createParameterDefinitions(savedWorkSteps);

        return savedWorkSteps;
    }

    /**
     * 为工作步骤创建参数定义
     * <p>
     * 批量查询ProcessStep下的stepParameter对象，
     * 根据workStep及stepParameter创建ParameterDefinition对象
     * </p>
     *
     * @param workSteps 工作步骤列表
     */
    private void createParameterDefinitions(List<WorkStep> workSteps) {
        if (CollectionUtils.isEmpty(workSteps)) {
            return;
        }

        // 收集所有ProcessStep的ID
        List<String> stepIds = workSteps.stream()
                .map(workStep -> workStep.getStep().getId())
                .toList();

        // 批量查询所有工步的参数
        List<StepParameter> allStepParameters = stepParameterRepository
                .findByStepIdInOrderByStepIdAscCreatedTimeAsc(stepIds);

        if (CollectionUtils.isEmpty(allStepParameters)) {
            log.info("任务 {} 的工步中没有找到参数定义", workSteps.get(0).getTask().getTaskCode());
            return;
        }

        // 按stepId分组，提高查找效率
        Map<String, List<StepParameter>> stepParameterMap = allStepParameters.stream()
                .collect(Collectors.groupingBy(param -> param.getStep().getId()));

        // 创建WorkStep的映射，便于快速查找
        Map<String, WorkStep> workStepMap = workSteps.stream()
                .collect(Collectors.toMap(ws -> ws.getStep().getId(), ws -> ws));

        List<ParameterDefinition> parameterDefinitions = new ArrayList<>();

        // 遍历参数映射，创建ParameterDefinition
        stepParameterMap.forEach((stepId, stepParameters) -> {
            WorkStep workStep = workStepMap.get(stepId);
            if (workStep != null) {
                for (StepParameter stepParameter : stepParameters) {
                    ParameterDefinition parameterDefinition = new ParameterDefinition();
                    parameterDefinition.setWorkStep(workStep);
                    parameterDefinition.setParamName(stepParameter.getParamName());
                    parameterDefinition.setParamCodee(stepParameter.getParamCode());
                    parameterDefinition.setParamType(stepParameter.getParamType());

                    parameterDefinitions.add(parameterDefinition);
                }
            }
        });

        // 批量保存参数定义
        if (!CollectionUtils.isEmpty(parameterDefinitions)) {
            parameterDefinitionRepository.saveAll(parameterDefinitions);
            log.info("为任务 {} 创建了 {} 个参数定义",
                    workSteps.get(0).getTask().getTaskCode(),
                    parameterDefinitions.size());
        }
    }
}
