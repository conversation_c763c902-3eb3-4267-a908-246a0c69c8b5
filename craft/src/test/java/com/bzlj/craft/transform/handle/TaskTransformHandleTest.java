package com.bzlj.craft.transform.handle;

import com.bzlj.craft.transform.entity.Telegram;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskTransformHandle 测试类
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@ExtendWith(MockitoExtension.class)
class TaskTransformHandleTest {

    @Mock
    private TelegramRepository telegramRepository;

    @InjectMocks
    private TaskTransformHandle taskTransformHandle;

    @Test
    void testAddTaskCodeToPayload_SingleObject() {
        // 准备测试数据
        String originalPayload = "{\"name\":\"test\",\"value\":123}";
        List<String> taskCodes = Arrays.asList("TASK001", "TASK002");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = TaskTransformHandle.class.getDeclaredMethod(
                "addTaskCodeToPayload", String.class, List.class);
            method.setAccessible(true);
            
            String result = (String) method.invoke(taskTransformHandle, originalPayload, taskCodes);
            
            // 验证结果
            JsonNode resultNode = JsonUtils.toJsonNode(result);
            assertTrue(resultNode.has("taskCode"));
            assertEquals("TASK001", resultNode.get("taskCode").asText());
            assertEquals("test", resultNode.get("name").asText());
            assertEquals(123, resultNode.get("value").asInt());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testAddTaskCodeToPayload_ArrayObject() {
        // 准备测试数据
        String originalPayload = "[{\"name\":\"test1\",\"value\":111},{\"name\":\"test2\",\"value\":222}]";
        List<String> taskCodes = Arrays.asList("TASK001", "TASK002");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = TaskTransformHandle.class.getDeclaredMethod(
                "addTaskCodeToPayload", String.class, List.class);
            method.setAccessible(true);
            
            String result = (String) method.invoke(taskTransformHandle, originalPayload, taskCodes);
            
            // 验证结果
            JsonNode resultNode = JsonUtils.toJsonNode(result);
            assertTrue(resultNode.isArray());
            assertEquals(2, resultNode.size());
            
            // 验证第一个元素
            JsonNode firstElement = resultNode.get(0);
            assertTrue(firstElement.has("taskCode"));
            assertEquals("TASK001", firstElement.get("taskCode").asText());
            assertEquals("test1", firstElement.get("name").asText());
            
            // 验证第二个元素
            JsonNode secondElement = resultNode.get(1);
            assertTrue(secondElement.has("taskCode"));
            assertEquals("TASK002", secondElement.get("taskCode").asText());
            assertEquals("test2", secondElement.get("name").asText());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testAddTaskCodeToPayload_EmptyTaskCodes() {
        // 准备测试数据
        String originalPayload = "{\"name\":\"test\",\"value\":123}";
        List<String> taskCodes = Arrays.asList();

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = TaskTransformHandle.class.getDeclaredMethod(
                "addTaskCodeToPayload", String.class, List.class);
            method.setAccessible(true);
            
            String result = (String) method.invoke(taskTransformHandle, originalPayload, taskCodes);
            
            // 验证结果 - 应该返回原始 payload，不添加 taskCode
            JsonNode resultNode = JsonUtils.toJsonNode(result);
            assertFalse(resultNode.has("taskCode"));
            assertEquals("test", resultNode.get("name").asText());
            assertEquals(123, resultNode.get("value").asInt());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    void testAddTaskCodeToPayload_InvalidJson() {
        // 准备测试数据
        String originalPayload = "invalid json";
        List<String> taskCodes = Arrays.asList("TASK001");

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = TaskTransformHandle.class.getDeclaredMethod(
                "addTaskCodeToPayload", String.class, List.class);
            method.setAccessible(true);
            
            String result = (String) method.invoke(taskTransformHandle, originalPayload, taskCodes);
            
            // 验证结果 - 应该返回原始 payload
            assertEquals(originalPayload, result);
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
}
